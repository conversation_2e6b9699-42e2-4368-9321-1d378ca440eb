package com.chenminjie.sea.application.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * App网格DTO
 * @author: <PERSON><PERSON>in<PERSON><PERSON>
 * @create: 2025/08/12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AppGridDto {
    
    private Long id;
    
    private Long strategyId;
    
    private Long userId;
    
    private Integer gridIndex; // 档位序号 1,2,3
    
    private Integer gridLevel; // 档位：1000, 950 ... (乘以1000)
    
    private Integer buyPrice; // 按档位计算出的理论买入价格 * 1000
    
    private Integer buyShares; // 不超过买入金额的最大买入股数
    
    private Integer sellPrice; // 按档位计算出的理论卖出价格 * 1000
    
    private Integer sellShares; // 卖出股数
    
    private Integer investment; // 当前档位应该买入金额 * 1000
    
    private Integer status; // 0 持有，1 已卖出
    
    private Date createdAt;
    
    private Date updatedAt;
}
