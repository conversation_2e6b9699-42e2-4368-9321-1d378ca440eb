package com.chenminjie.sea.application.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * App策略DTO
 * @author: Chen<PERSON>in<PERSON><PERSON>
 * @create: 2025/08/12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AppStrategyDto {
    
    private Long id;
    
    private Long userId;
    
    private String name;
    
    private String stockCode;
    
    private Integer gridStep; // 网格间距 * 1000
    
    private Integer basePrice; // 基准价格 * 1000
    
    private Integer upperPrice; // 价格上限 * 1000
    
    private Integer lowerPrice; // 价格下限 * 1000
    
    private Integer baseInvestment; // 第一网的初始买入金额 * 1000
    
    private Integer buyStrategy; // 买入策略: 1=等额, 2=金额递增, 3=固定股数
    
    private Integer investmentIncrementRatio; // 递增比例 * 1000
    
    private Integer quantityPerGrid; // 固定股数
    
    private Integer gridType; // 1=等差, 2=等比
    
    // 中大网格配置
    private Boolean mediumGridEnabled;
    
    private Integer mediumGridStep; // 中网间距 * 1000
    
    private Boolean largeGridEnabled;
    
    private Integer largeGridStep; // 大网间距 * 1000
    
    // 通知设置
    private Boolean triggerNotify;
    
    // 利息计算
    private Boolean calculateInterestEnable;
    
    private Integer interestPeriod; // 1=月, 2=年
    
    private Integer interestRate; // 利率 * 1000
    
    // 预留利润
    private Boolean profitRetentionEnabled;
    
    private Integer profitRetentionMultiple; // 预留利润倍数 * 1000
    
    private Integer retainShares; // 预留股数
    
    // 策略状态
    private Integer status; // 1=启用, 2=归档, 3=停用
    
    private Boolean customizeGridEnable; // 是否开启自定义网格
    
    private String customizeGridConfig; // JSON格式的自定义网格配置
    
    // 统计信息
    private Integer nextSellGridIndex;
    
    private Integer nextBuyGridIndex;
    
    private Integer totalInvestment;
    
    private Integer realizedPnl; // 通过已完成网格买卖交易产生的累计盈亏
    
    private Date createdAt;
    
    private Date updatedAt;
}
