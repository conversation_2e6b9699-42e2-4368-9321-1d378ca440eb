package com.chenminjie.sea.application.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * App交易日志DTO
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2025/08/12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AppTradeLogDto {
    
    private Long id;
    
    private Long strategyId;
    
    private Long gridId;
    
    private Integer tradeType; // 交易类型，0=买，1=卖
    
    private Integer tradeShares; // 交易股数
    
    private Integer tradePrice; // 交易价格 * 1000
    
    private String mood; // 心情
    
    private String note; // 备注
    
    private Long accountId; // 券商账户id
    
    private Date createdAt;
    
    private Date updatedAt;
}
