package com.chenminjie.sea.application.service;

import com.chenminjie.sea.application.dto.AppGridDto;
import com.chenminjie.sea.common.dto.CreateAppGridRequest;
import com.chenminjie.sea.common.dto.UpdateAppGridRequest;

import java.util.List;

/**
 * App网格服务接口
 * @author: <PERSON><PERSON>in<PERSON><PERSON>
 * @create: 2025/08/12
 */
public interface AppGridService {

    /**
     * 创建网格（买入）
     */
    AppGridDto createGrid(CreateAppGridRequest request);

    /**
     * 获取策略下的网格列表
     */
    List<AppGridDto> getGridsByStrategy(Long userId, Long strategyId, Integer status);

    /**
     * 获取网格详情
     */
    AppGridDto getGrid(Long userId, Long gridId);

    /**
     * 更新网格
     */
    void updateGrid(Long gridId, UpdateAppGridRequest request, Long userId);

    /**
     * 卖出网格
     */
    void sellGrid(Long gridId, Integer sellPrice, Integer sellShares, Long userId);

    /**
     * 删除网格
     */
    void deleteGrid(Long gridId, Long userId);

    /**
     * 批量创建网格（根据策略配置自动生成）
     */
    List<AppGridDto> batchCreateGrids(Long strategyId, Integer gridCount, Long userId);

    /**
     * 获取下一个买入网格
     */
    AppGridDto getNextBuyGrid(Long userId, Long strategyId);

    /**
     * 获取下一个卖出网格
     */
    AppGridDto getNextSellGrid(Long userId, Long strategyId);

    /**
     * 根据价格获取触发的网格
     */
    List<AppGridDto> getTriggeredGrids(Long userId, Long strategyId, Integer currentPrice);
}
