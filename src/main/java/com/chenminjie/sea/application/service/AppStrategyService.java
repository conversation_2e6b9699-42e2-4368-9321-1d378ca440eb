package com.chenminjie.sea.application.service;

import com.chenminjie.sea.application.dto.AppStrategyDto;
import com.chenminjie.sea.common.dto.CreateAppStrategyRequest;
import com.chenminjie.sea.common.dto.QueryPagedResponse;
import com.chenminjie.sea.common.dto.UpdateAppStrategyRequest;

/**
 * App策略服务接口
 * @author: <PERSON><PERSON>in<PERSON><PERSON>
 * @create: 2025/08/12
 */
public interface AppStrategyService {

    /**
     * 创建策略
     */
    AppStrategyDto createStrategy(CreateAppStrategyRequest request);

    /**
     * 获取分页策略列表
     */
    QueryPagedResponse<AppStrategyDto> getPagedStrategies(Long userId, Integer pageNumber, Integer pageSize, Integer status);

    /**
     * 获取策略详情
     */
    AppStrategyDto getStrategy(Long userId, Long strategyId);

    /**
     * 更新策略
     */
    void updateStrategy(Long strategyId, UpdateAppStrategyRequest request, Long userId);

    /**
     * 删除策略
     */
    void deleteStrategy(Long strategyId, Long userId);

    /**
     * 归档策略
     */
    void archiveStrategy(Long strategyId, Long userId);

    /**
     * 启用策略
     */
    void enableStrategy(Long strategyId, Long userId);

    /**
     * 停用策略
     */
    void disableStrategy(Long strategyId, Long userId);

    /**
     * 计算策略统计信息
     */
    void calculateStrategyStatistics(Long strategyId, Long userId);

    /**
     * 重新计算网格
     */
    void recalculateGrids(Long strategyId, Long userId);
}
