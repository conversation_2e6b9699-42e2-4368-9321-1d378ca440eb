package com.chenminjie.sea.application.service;

import com.chenminjie.sea.application.dto.AppTradeLogDto;
import com.chenminjie.sea.common.dto.CreateAppTradeLogRequest;
import com.chenminjie.sea.common.dto.QueryPagedResponse;
import com.chenminjie.sea.common.dto.UpdateAppTradeLogRequest;

import java.util.List;

/**
 * App交易日志服务接口
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2025/08/12
 */
public interface AppTradeLogService {

    /**
     * 创建交易日志
     */
    AppTradeLogDto createTradeLog(CreateAppTradeLogRequest request);

    /**
     * 获取分页交易日志
     */
    QueryPagedResponse<AppTradeLogDto> getPagedTradeLogs(Long userId, Long strategyId, Long gridId, 
                                                        Integer tradeType, Integer pageNumber, Integer pageSize);

    /**
     * 获取交易日志详情
     */
    AppTradeLogDto getTradeLog(Long userId, Long tradeLogId);

    /**
     * 更新交易日志
     */
    void updateTradeLog(Long tradeLogId, UpdateAppTradeLogRequest request, Long userId);

    /**
     * 删除交易日志
     */
    void deleteTradeLog(Long tradeLogId, Long userId);

    /**
     * 获取网格的交易日志
     */
    List<AppTradeLogDto> getTradeLogsByGrid(Long userId, Long gridId);

    /**
     * 获取策略的交易日志
     */
    List<AppTradeLogDto> getTradeLogsByStrategy(Long userId, Long strategyId);

    /**
     * 按日期范围获取交易日志
     */
    List<AppTradeLogDto> getTradeLogsByDateRange(Long userId, String startDate, String endDate, Long strategyId);

    /**
     * 计算策略的盈亏
     */
    Integer calculateStrategyPnl(Long userId, Long strategyId);

    /**
     * 计算网格的盈亏
     */
    Integer calculateGridPnl(Long userId, Long gridId);
}
