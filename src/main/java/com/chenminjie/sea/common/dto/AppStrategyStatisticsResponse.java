package com.chenminjie.sea.common.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * App策略统计响应
 * @author: ChenMinJie
 * @create: 2025/08/12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AppStrategyStatisticsResponse {

    /**
     * 策略ID
     */
    private Long strategyId;

    /**
     * 策略名称
     */
    private String strategyName;

    /**
     * 股票代码
     */
    private String stockCode;

    /**
     * 当前总投入金额（从未卖出网格统计）* 1000
     */
    private Integer totalInvestment;

    /**
     * 网格卖出次数
     */
    private Integer gridSellCount;

    /**
     * 累计收益（从已卖出网格统计）* 1000
     */
    private Integer totalProfit;

    /**
     * 总持有股数
     */
    private Integer totalHoldingShares;

    /**
     * 预留利润股数（已卖出网格中剩余的股数）
     */
    private Integer retainProfitShares;

    /**
     * 当前持有网格数量
     */
    private Integer holdingGridCount;

    /**
     * 已卖出网格数量
     */
    private Integer soldGridCount;

    /**
     * 平均持仓成本 * 1000
     */
    private Integer averageCost;

    /**
     * 当前盈亏 * 1000
     */
    private Integer currentPnl;

    /**
     * 盈亏比例 * 1000 (百分比)
     */
    private Integer pnlRatio;

    /**
     * 年化收益率 * 1000 (百分比)
     */
    private Integer annualizedReturn;

    /**
     * 策略运行天数
     */
    private Integer runningDays;
}
