package com.chenminjie.sea.common.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 创建App网格请求
 * @author: ChenMinJie
 * @create: 2025/08/12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateAppGridRequest {
    
    @NotNull(message = "策略ID不能为空")
    private Long strategyId;
    
    @NotNull(message = "档位序号不能为空")
    @Min(value = 1, message = "档位序号应大于 0")
    @Max(value = 999, message = "档位序号过大")
    private Integer gridIndex; // 档位序号 1,2,3
    
    @NotNull(message = "档位不能为空")
    @Min(value = 1, message = "档位应大于 0")
    @Max(value = 999999, message = "档位过大")
    private Integer gridLevel; // 档位：1000, 950 ... (乘以1000)
    
    @NotNull(message = "买入价格不能为空")
    @Min(value = 1, message = "买入价格应大于 0")
    @Max(value = Integer.MAX_VALUE / 2, message = "买入价格过大")
    private Integer buyPrice; // 按档位计算出的理论买入价格 * 1000
    
    @NotNull(message = "买入股数不能为空")
    @Min(value = 0, message = "买入股数应大于等于 0")
    @Max(value = Integer.MAX_VALUE / 2, message = "买入股数过大")
    private Integer buyShares; // 不超过买入金额的最大买入股数
    
    @NotNull(message = "卖出价格不能为空")
    @Min(value = 1, message = "卖出价格应大于 0")
    @Max(value = Integer.MAX_VALUE / 2, message = "卖出价格过大")
    private Integer sellPrice; // 按档位计算出的理论卖出价格 * 1000
    
    @NotNull(message = "卖出股数不能为空")
    @Min(value = 0, message = "卖出股数应大于等于 0")
    @Max(value = Integer.MAX_VALUE / 2, message = "卖出股数过大")
    private Integer sellShares; // 卖出股数
    
    @NotNull(message = "投资金额不能为空")
    @Min(value = 1, message = "投资金额应大于 0")
    @Max(value = Integer.MAX_VALUE / 2, message = "投资金额过大")
    private Integer investment; // 当前档位应该买入金额 * 1000
}
