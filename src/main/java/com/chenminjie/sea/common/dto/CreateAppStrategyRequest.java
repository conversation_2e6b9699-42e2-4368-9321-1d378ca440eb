package com.chenminjie.sea.common.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.*;

/**
 * 创建App策略请求
 * @author: <PERSON><PERSON>in<PERSON><PERSON>
 * @create: 2025/08/12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateAppStrategyRequest {
    
    @NotBlank(message = "策略名称不能为空")
    @Length(max = 64, message = "策略名称过长")
    private String name;
    
    @Length(max = 20, message = "股票代码过长")
    private String stockCode;
    
    @NotNull(message = "网格间距不能为空")
    @Min(value = 1, message = "网格间距应大于 0")
    @Max(value = 999999, message = "网格间距过大")
    private Integer gridStep; // 网格间距 * 1000
    
    @NotNull(message = "基准价格不能为空")
    @Min(value = 1, message = "基准价格应大于 0")
    @Max(value = Integer.MAX_VALUE / 2, message = "基准价格过大")
    private Integer basePrice; // 基准价格 * 1000
    
    @NotNull(message = "价格上限不能为空")
    @Min(value = 1, message = "价格上限应大于 0")
    @Max(value = Integer.MAX_VALUE / 2, message = "价格上限过大")
    private Integer upperPrice; // 价格上限 * 1000
    
    @NotNull(message = "价格下限不能为空")
    @Min(value = 1, message = "价格下限应大于 0")
    @Max(value = Integer.MAX_VALUE / 2, message = "价格下限过大")
    private Integer lowerPrice; // 价格下限 * 1000
    
    @NotNull(message = "初始买入金额不能为空")
    @Min(value = 1, message = "初始买入金额应大于 0")
    @Max(value = Integer.MAX_VALUE / 2, message = "初始买入金额过大")
    private Integer baseInvestment; // 第一网的初始买入金额 * 1000
    
    @NotNull(message = "买入策略不能为空")
    @Min(value = 1, message = "买入策略不合法")
    @Max(value = 3, message = "买入策略不合法")
    private Integer buyStrategy; // 买入策略: 1=等额, 2=金额递增, 3=固定股数
    
    @Min(value = 1, message = "递增比例应大于 0")
    @Max(value = Integer.MAX_VALUE / 2, message = "递增比例过大")
    private Integer investmentIncrementRatio; // 递增比例 * 1000
    
    @Min(value = 1, message = "固定股数应大于 0")
    @Max(value = Integer.MAX_VALUE / 2, message = "固定股数过大")
    private Integer quantityPerGrid; // 固定股数
    
    @NotNull(message = "网格类型不能为空")
    @Min(value = 1, message = "网格类型不合法")
    @Max(value = 2, message = "网格类型不合法")
    private Integer gridType; // 1=等差, 2=等比
    
    // 中大网格配置
    private Boolean mediumGridEnabled;
    
    @Min(value = 1, message = "中网间距应大于 0")
    @Max(value = 999999, message = "中网间距过大")
    private Integer mediumGridStep; // 中网间距 * 1000
    
    private Boolean largeGridEnabled;
    
    @Min(value = 1, message = "大网间距应大于 0")
    @Max(value = 999999, message = "大网间距过大")
    private Integer largeGridStep; // 大网间距 * 1000
    
    // 通知设置
    private Boolean triggerNotify;
    
    // 利息计算
    private Boolean calculateInterestEnable;
    
    @Min(value = 1, message = "利息周期不合法")
    @Max(value = 2, message = "利息周期不合法")
    private Integer interestPeriod; // 1=月, 2=年
    
    @Min(value = 1, message = "利率应大于 0")
    @Max(value = 999999, message = "利率过大")
    private Integer interestRate; // 利率 * 1000
    
    // 预留利润
    private Boolean profitRetentionEnabled;
    
    @Min(value = 1, message = "预留利润倍数应大于 0")
    @Max(value = 999999, message = "预留利润倍数过大")
    private Integer profitRetentionMultiple; // 预留利润倍数 * 1000
    
    @Min(value = 1, message = "预留股数应大于 0")
    @Max(value = Integer.MAX_VALUE / 2, message = "预留股数过大")
    private Integer retainShares; // 预留股数
    
    // 自定义网格
    private Boolean customizeGridEnable;
    
    @Length(max = 2000, message = "自定义网格配置过长")
    private String customizeGridConfig; // JSON格式的自定义网格配置
}
