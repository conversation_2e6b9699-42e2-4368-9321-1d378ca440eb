package com.chenminjie.sea.common.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * 更新App网格请求
 * @author: <PERSON><PERSON>in<PERSON><PERSON>
 * @create: 2025/08/12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateAppGridRequest {
    
    @Min(value = 1, message = "档位序号应大于 0")
    @Max(value = 999, message = "档位序号过大")
    private Integer gridIndex; // 档位序号 1,2,3
    
    @Min(value = 1, message = "档位应大于 0")
    @Max(value = 999999, message = "档位过大")
    private Integer gridLevel; // 档位：1000, 950 ... (乘以1000)
    
    @Min(value = 1, message = "买入价格应大于 0")
    @Max(value = Integer.MAX_VALUE / 2, message = "买入价格过大")
    private Integer buyPrice; // 按档位计算出的理论买入价格 * 1000
    
    @Min(value = 0, message = "买入股数应大于等于 0")
    @Max(value = Integer.MAX_VALUE / 2, message = "买入股数过大")
    private Integer buyShares; // 不超过买入金额的最大买入股数
    
    @Min(value = 1, message = "卖出价格应大于 0")
    @Max(value = Integer.MAX_VALUE / 2, message = "卖出价格过大")
    private Integer sellPrice; // 按档位计算出的理论卖出价格 * 1000
    
    @Min(value = 0, message = "卖出股数应大于等于 0")
    @Max(value = Integer.MAX_VALUE / 2, message = "卖出股数过大")
    private Integer sellShares; // 卖出股数
    
    @Min(value = 1, message = "投资金额应大于 0")
    @Max(value = Integer.MAX_VALUE / 2, message = "投资金额过大")
    private Integer investment; // 当前档位应该买入金额 * 1000
    
    @Min(value = 0, message = "状态不合法")
    @Max(value = 1, message = "状态不合法")
    private Integer status; // 0 持有，1 已卖出
}
