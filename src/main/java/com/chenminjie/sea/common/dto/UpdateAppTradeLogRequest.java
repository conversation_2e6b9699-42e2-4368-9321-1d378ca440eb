package com.chenminjie.sea.common.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * 更新App交易日志请求
 * @author: ChenMinJie
 * @create: 2025/08/12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateAppTradeLogRequest {
    
    private Long gridId; // 可以为空，表示非网格交易
    
    @Min(value = 0, message = "交易类型不合法")
    @Max(value = 1, message = "交易类型不合法")
    private Integer tradeType; // 交易类型，0=买，1=卖
    
    @Min(value = 1, message = "交易股数应大于 0")
    @Max(value = Integer.MAX_VALUE / 2, message = "交易股数过大")
    private Integer tradeShares; // 交易股数
    
    @Min(value = 1, message = "交易价格应大于 0")
    @Max(value = Integer.MAX_VALUE / 2, message = "交易价格过大")
    private Integer tradePrice; // 交易价格 * 1000
    
    @Length(max = 24, message = "心情描述过长")
    private String mood; // 心情
    
    @Length(max = 256, message = "备注过长")
    private String note; // 备注
    
    private Long accountId; // 券商账户id
}
