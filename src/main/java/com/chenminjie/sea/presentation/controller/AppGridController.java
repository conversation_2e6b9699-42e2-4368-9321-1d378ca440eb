package com.chenminjie.sea.presentation.controller;

import com.chenminjie.sea.application.dto.AppGridDto;
import com.chenminjie.sea.application.service.AppGridService;
import com.chenminjie.sea.common.dto.CreateAppGridRequest;
import com.chenminjie.sea.common.dto.UpdateAppGridRequest;
import com.chenminjie.sea.common.util.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;

/**
 * App网格控制器
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2025/08/12
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/api/v1/app/grids")
public class AppGridController {

    @Autowired
    private AppGridService appGridService;

    /**
     * 创建网格（买入）
     */
    @PostMapping("")
    public ResponseEntity<AppGridDto> createGrid(@RequestHeader("Authorization") String token,
                                                @Valid @RequestBody CreateAppGridRequest request) throws URISyntaxException {
        log.info("create app grid request: {}", request);
        AppGridDto gridDto = appGridService.createGrid(request);
        return ResponseEntity.created(new URI("/api/v1/app/grids/" + gridDto.getId())).body(gridDto);
    }

    /**
     * 获取策略下的所有网格
     */
    @GetMapping("")
    public ResponseEntity<List<AppGridDto>> getGridsByStrategy(
            @RequestHeader("Authorization") String token,
            @RequestParam(value = "strategyId") @NotNull Long strategyId,
            @RequestParam(value = "status", required = false) Integer status) {
        
        log.info("get app grids by strategy: strategyId({}), status({})", strategyId, status);
        List<AppGridDto> grids = appGridService.getGridsByStrategy(UserContext.getUserId(), strategyId, status);
        return ResponseEntity.ok(grids);
    }

    /**
     * 获取单个网格详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<AppGridDto> getGrid(@RequestHeader("Authorization") String token,
                                             @PathVariable("id") Long id) {
        log.info("get app grid: {}", id);
        AppGridDto gridDto = appGridService.getGrid(UserContext.getUserId(), id);
        return ResponseEntity.ok(gridDto);
    }

    /**
     * 更新网格
     */
    @PutMapping("/{id}")
    public ResponseEntity<Void> updateGrid(@RequestHeader("Authorization") String token,
                                          @PathVariable("id") Long id,
                                          @Valid @RequestBody UpdateAppGridRequest request) {
        log.info("update app grid: {}", id);
        log.info("update app grid request: {}", request);
        appGridService.updateGrid(id, request, UserContext.getUserId());
        return ResponseEntity.noContent().build();
    }

    /**
     * 卖出网格
     */
    @PutMapping("/{id}/sell")
    public ResponseEntity<Void> sellGrid(@RequestHeader("Authorization") String token,
                                        @PathVariable("id") Long id,
                                        @RequestParam("sellPrice") Integer sellPrice,
                                        @RequestParam("sellShares") Integer sellShares) {
        log.info("sell app grid: {}, sellPrice: {}, sellShares: {}", id, sellPrice, sellShares);
        appGridService.sellGrid(id, sellPrice, sellShares, UserContext.getUserId());
        return ResponseEntity.noContent().build();
    }

    /**
     * 删除网格
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteGrid(@RequestHeader("Authorization") String token,
                                          @PathVariable("id") Long id) {
        log.info("delete app grid: {}", id);
        appGridService.deleteGrid(id, UserContext.getUserId());
        return ResponseEntity.noContent().build();
    }

    /**
     * 批量创建网格（根据策略配置自动生成）
     */
    @PostMapping("/batch")
    public ResponseEntity<List<AppGridDto>> batchCreateGrids(
            @RequestHeader("Authorization") String token,
            @RequestParam("strategyId") @NotNull Long strategyId,
            @RequestParam(value = "gridCount", defaultValue = "10") Integer gridCount) {
        
        log.info("batch create app grids: strategyId({}), gridCount({})", strategyId, gridCount);
        List<AppGridDto> grids = appGridService.batchCreateGrids(strategyId, gridCount, UserContext.getUserId());
        return ResponseEntity.ok(grids);
    }
}
