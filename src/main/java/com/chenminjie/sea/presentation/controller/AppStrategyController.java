package com.chenminjie.sea.presentation.controller;

import com.chenminjie.sea.application.dto.AppStrategyDto;
import com.chenminjie.sea.application.service.AppStrategyService;
import com.chenminjie.sea.common.dto.CreateAppStrategyRequest;
import com.chenminjie.sea.common.dto.QueryPagedResponse;
import com.chenminjie.sea.common.dto.UpdateAppStrategyRequest;
import com.chenminjie.sea.common.util.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;

/**
 * App策略控制器
 * @author: <PERSON><PERSON>in<PERSON><PERSON>
 * @create: 2025/08/12
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/api/v1/app/strategies")
public class AppStrategyController {

    @Autowired
    private AppStrategyService appStrategyService;

    /**
     * 创建策略
     */
    @PostMapping("")
    public ResponseEntity<AppStrategyDto> createStrategy(@RequestHeader("Authorization") String token,
                                                        @Valid @RequestBody CreateAppStrategyRequest request) throws URISyntaxException {
        log.info("create app strategy request: {}", request);
        AppStrategyDto strategyDto = appStrategyService.createStrategy(request);
        return ResponseEntity.created(new URI("/api/v1/app/strategies/" + strategyDto.getId())).body(strategyDto);
    }

    /**
     * 获取策略列表（分页）
     */
    @GetMapping("")
    public ResponseEntity<QueryPagedResponse<AppStrategyDto>> getStrategies(
            @RequestHeader("Authorization") String token,
            @RequestParam(value = "pageNumber", defaultValue = "1") Integer pageNumber,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(value = "status", required = false) Integer status) {
        
        log.info("get app strategies: pageNumber({}), pageSize({}), status({})", pageNumber, pageSize, status);
        
        if (pageNumber <= 0 || pageSize <= 0 || pageSize > 100) {
            return new ResponseEntity<>(null, HttpStatus.BAD_REQUEST);
        }
        
        QueryPagedResponse<AppStrategyDto> response = appStrategyService.getPagedStrategies(
                UserContext.getUserId(), pageNumber, pageSize, status);
        return ResponseEntity.ok(response);
    }

    /**
     * 获取单个策略详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<AppStrategyDto> getStrategy(@RequestHeader("Authorization") String token,
                                                     @PathVariable("id") Long id) {
        log.info("get app strategy: {}", id);
        AppStrategyDto strategyDto = appStrategyService.getStrategy(UserContext.getUserId(), id);
        return ResponseEntity.ok(strategyDto);
    }

    /**
     * 更新策略
     */
    @PutMapping("/{id}")
    public ResponseEntity<Void> updateStrategy(@RequestHeader("Authorization") String token,
                                              @PathVariable("id") Long id,
                                              @Valid @RequestBody UpdateAppStrategyRequest request) {
        log.info("update app strategy: {}", id);
        log.info("update app strategy request: {}", request);
        appStrategyService.updateStrategy(id, request, UserContext.getUserId());
        return ResponseEntity.noContent().build();
    }

    /**
     * 删除策略
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteStrategy(@RequestHeader("Authorization") String token,
                                              @PathVariable("id") Long id) {
        log.info("delete app strategy: {}", id);
        appStrategyService.deleteStrategy(id, UserContext.getUserId());
        return ResponseEntity.noContent().build();
    }

    /**
     * 归档策略
     */
    @PutMapping("/{id}/archive")
    public ResponseEntity<Void> archiveStrategy(@RequestHeader("Authorization") String token,
                                               @PathVariable("id") Long id) {
        log.info("archive app strategy: {}", id);
        appStrategyService.archiveStrategy(id, UserContext.getUserId());
        return ResponseEntity.noContent().build();
    }

    /**
     * 启用策略
     */
    @PutMapping("/{id}/enable")
    public ResponseEntity<Void> enableStrategy(@RequestHeader("Authorization") String token,
                                              @PathVariable("id") Long id) {
        log.info("enable app strategy: {}", id);
        appStrategyService.enableStrategy(id, UserContext.getUserId());
        return ResponseEntity.noContent().build();
    }

    /**
     * 停用策略
     */
    @PutMapping("/{id}/disable")
    public ResponseEntity<Void> disableStrategy(@RequestHeader("Authorization") String token,
                                               @PathVariable("id") Long id) {
        log.info("disable app strategy: {}", id);
        appStrategyService.disableStrategy(id, UserContext.getUserId());
        return ResponseEntity.noContent().build();
    }
}
