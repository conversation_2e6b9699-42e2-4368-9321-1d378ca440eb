package com.chenminjie.sea.presentation.controller;

import com.chenminjie.sea.application.dto.AppTradeLogDto;
import com.chenminjie.sea.application.service.AppTradeLogService;
import com.chenminjie.sea.common.dto.CreateAppTradeLogRequest;
import com.chenminjie.sea.common.dto.QueryPagedResponse;
import com.chenminjie.sea.common.dto.UpdateAppTradeLogRequest;
import com.chenminjie.sea.common.util.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;

/**
 * App交易日志控制器
 * @author: <PERSON><PERSON>in<PERSON><PERSON>
 * @create: 2025/08/12
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/api/v1/app/tradeLogs")
public class AppTradeLogController {

    @Autowired
    private AppTradeLogService appTradeLogService;

    /**
     * 创建交易日志
     */
    @PostMapping("")
    public ResponseEntity<AppTradeLogDto> createTradeLog(@RequestHeader("Authorization") String token,
                                                        @Valid @RequestBody CreateAppTradeLogRequest request) throws URISyntaxException {
        log.info("create app trade log request: {}", request);
        AppTradeLogDto tradeLogDto = appTradeLogService.createTradeLog(request);
        return ResponseEntity.created(new URI("/api/v1/app/tradeLogs/" + tradeLogDto.getId())).body(tradeLogDto);
    }

    /**
     * 获取策略下的交易日志（分页）
     */
    @GetMapping("")
    public ResponseEntity<QueryPagedResponse<AppTradeLogDto>> getTradeLogsByStrategy(
            @RequestHeader("Authorization") String token,
            @RequestParam(value = "strategyId", required = false) Long strategyId,
            @RequestParam(value = "gridId", required = false) Long gridId,
            @RequestParam(value = "tradeType", required = false) Integer tradeType,
            @RequestParam(value = "pageNumber", defaultValue = "1") Integer pageNumber,
            @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize) {
        
        log.info("get app trade logs: strategyId({}), gridId({}), tradeType({}), pageNumber({}), pageSize({})", 
                strategyId, gridId, tradeType, pageNumber, pageSize);
        
        QueryPagedResponse<AppTradeLogDto> response = appTradeLogService.getPagedTradeLogs(
                UserContext.getUserId(), strategyId, gridId, tradeType, pageNumber, pageSize);
        return ResponseEntity.ok(response);
    }

    /**
     * 获取单个交易日志详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<AppTradeLogDto> getTradeLog(@RequestHeader("Authorization") String token,
                                                     @PathVariable("id") Long id) {
        log.info("get app trade log: {}", id);
        AppTradeLogDto tradeLogDto = appTradeLogService.getTradeLog(UserContext.getUserId(), id);
        return ResponseEntity.ok(tradeLogDto);
    }

    /**
     * 更新交易日志
     */
    @PutMapping("/{id}")
    public ResponseEntity<Void> updateTradeLog(@RequestHeader("Authorization") String token,
                                              @PathVariable("id") Long id,
                                              @Valid @RequestBody UpdateAppTradeLogRequest request) {
        log.info("update app trade log: {}", id);
        log.info("update app trade log request: {}", request);
        appTradeLogService.updateTradeLog(id, request, UserContext.getUserId());
        return ResponseEntity.noContent().build();
    }

    /**
     * 删除交易日志
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteTradeLog(@RequestHeader("Authorization") String token,
                                              @PathVariable("id") Long id) {
        log.info("delete app trade log: {}", id);
        appTradeLogService.deleteTradeLog(id, UserContext.getUserId());
        return ResponseEntity.noContent().build();
    }

    /**
     * 获取网格的交易日志
     */
    @GetMapping("/grid/{gridId}")
    public ResponseEntity<List<AppTradeLogDto>> getTradeLogsByGrid(
            @RequestHeader("Authorization") String token,
            @PathVariable("gridId") Long gridId) {
        
        log.info("get app trade logs by grid: {}", gridId);
        List<AppTradeLogDto> tradeLogs = appTradeLogService.getTradeLogsByGrid(UserContext.getUserId(), gridId);
        return ResponseEntity.ok(tradeLogs);
    }

    /**
     * 获取用户的所有交易日志（按日期范围）
     */
    @GetMapping("/dateRange")
    public ResponseEntity<List<AppTradeLogDto>> getTradeLogsByDateRange(
            @RequestHeader("Authorization") String token,
            @RequestParam(value = "startDate") String startDate,
            @RequestParam(value = "endDate") String endDate,
            @RequestParam(value = "strategyId", required = false) Long strategyId) {
        
        log.info("get app trade logs by date range: startDate({}), endDate({}), strategyId({})", 
                startDate, endDate, strategyId);
        
        List<AppTradeLogDto> tradeLogs = appTradeLogService.getTradeLogsByDateRange(
                UserContext.getUserId(), startDate, endDate, strategyId);
        return ResponseEntity.ok(tradeLogs);
    }
}
